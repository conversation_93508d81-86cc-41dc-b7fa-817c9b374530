import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Search, User, Sparkles } from 'lucide-react';

interface HeaderProps {
  title: string;
  onMenuClick: () => void;
}

const Header: React.FC<HeaderProps> = ({ title, onMenuClick }) => {
  return (
    <header className="relative bg-white/80 backdrop-blur-xl border-b border-white/20 shadow-lg px-4 py-4 lg:px-8">
      <div className="absolute inset-0 bg-gradient-to-r from-white/50 to-indigo-50/50"></div>

      <div className="relative z-10 flex items-center justify-between">
        {/* Left side - Menu button and title */}
        <div className="flex items-center space-x-4">
          <button onClick={onMenuClick} className="p-3 rounded-2xl text-gray-500 hover:text-gray-700 hover:bg-white/50 lg:hidden transition-all duration-200 shadow-md hover:shadow-lg">
            <Menu className="h-5 w-5" />
          </button>

          <div className="flex items-center space-x-3">
            <div className="hidden lg:flex items-center justify-center w-10 h-10 rounded-2xl bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg">
              <Sparkles className="h-5 w-5 text-white" />
            </div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">{title}</h1>
          </div>
        </div>

        {/* Right side - Search and notifications */}
        <div className="flex items-center space-x-4">
          {/* Modern Search bar */}
          <div className="hidden md:block relative">
            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search anything..."
              className="block w-80 pl-12 pr-4 py-3 bg-white/50 backdrop-blur-sm border border-white/20 rounded-2xl leading-5 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500/30 text-sm font-medium shadow-lg transition-all duration-200 hover:shadow-xl focus:shadow-xl"
            />
          </div>

          {/* Modern Notifications */}
          <button className="relative p-3 rounded-2xl text-gray-500 hover:text-gray-700 hover:bg-white/50 transition-all duration-200 shadow-md hover:shadow-lg group">
            <Bell className="h-5 w-5" />
            {/* Animated notification badge */}
            <span className="absolute -top-1 -right-1 flex h-4 w-4">
              <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
              <span className="relative inline-flex rounded-full h-4 w-4 bg-red-500 items-center justify-center">
                <span className="text-xs font-bold text-white">3</span>
              </span>
            </span>
          </button>

          {/* Modern User profile */}
          <div className="flex items-center space-x-3">
            <div className="relative group">
              <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-200 cursor-pointer group-hover:scale-105">
                <User className="h-5 w-5 text-white" />
              </div>
              <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white shadow-md"></div>
            </div>

            <div className="hidden lg:block">
              <p className="text-sm font-semibold text-gray-900">John Doe</p>
              <p className="text-xs text-gray-500">Premium User</p>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
