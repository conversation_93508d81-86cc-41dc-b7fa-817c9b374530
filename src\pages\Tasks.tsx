import React from 'react';
import { Plus, Filter, Search, Calendar, Clock, Star, CheckCircle2, Circle, MoreHorizontal } from 'lucide-react';

const Tasks: React.FC = () => {
  const tasks = [
    { id: 1, title: 'Complete project proposal', description: 'Finalize the Q4 project proposal for client review', priority: 'high', completed: false, dueDate: '2024-01-15', category: 'Work' },
    { id: 2, title: 'Review design mockups', description: 'Review and provide feedback on the new app designs', priority: 'medium', completed: true, dueDate: '2024-01-14', category: 'Design' },
    { id: 3, title: 'Schedule team meeting', description: 'Organize weekly team sync meeting', priority: 'low', completed: false, dueDate: '2024-01-16', category: 'Management' },
    { id: 4, title: 'Update documentation', description: 'Update API documentation with latest changes', priority: 'medium', completed: false, dueDate: '2024-01-17', category: 'Development' },
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="space-y-8 animate-fade-in-up">
      {/* Modern Header */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-emerald-500 via-teal-600 to-cyan-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-white/10 blur-xl animate-pulse"></div>
        <div className="absolute -bottom-4 -left-4 h-32 w-32 rounded-full bg-white/5 blur-2xl animate-pulse delay-1000"></div>

        <div className="relative z-10 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
              <CheckCircle2 className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Task Manager</h1>
              <p className="text-xl text-white/90 mt-1">Stay organized and productive</p>
            </div>
          </div>
          <div className="mt-4 sm:mt-0">
            <button className="btn-modern bg-white/20 backdrop-blur-sm text-white border-white/30 hover:bg-white/30 px-6 py-3 rounded-2xl font-semibold flex items-center space-x-2 transition-all duration-300 hover:scale-105">
              <Plus className="h-5 w-5" />
              <span>Add Task</span>
            </button>
          </div>
        </div>
      </div>

      {/* Modern Filters and Search */}
      <div className="rounded-3xl bg-white/80 backdrop-blur-xl p-6 shadow-xl border border-white/20">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-6">
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search tasks..."
                className="block w-full pl-12 pr-4 py-3 bg-white/50 backdrop-blur-sm border border-white/20 rounded-2xl leading-5 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-emerald-500/20 focus:border-emerald-500/30 text-sm font-medium shadow-lg transition-all duration-200"
              />
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button className="flex items-center space-x-2 px-4 py-3 bg-gray-50 hover:bg-gray-100 rounded-2xl text-sm font-medium text-gray-700 transition-all duration-200 hover:scale-105">
              <Filter className="h-4 w-4" />
              <span>Filter</span>
            </button>
            <button className="flex items-center space-x-2 px-4 py-3 bg-gray-50 hover:bg-gray-100 rounded-2xl text-sm font-medium text-gray-700 transition-all duration-200 hover:scale-105">
              <Calendar className="h-4 w-4" />
              <span>Due Date</span>
            </button>
          </div>
        </div>
      </div>

      {/* Modern Tasks Grid */}
      <div className="grid gap-6">
        {tasks.map((task, index) => (
          <div
            key={task.id}
            className="group relative overflow-hidden rounded-3xl bg-white/80 backdrop-blur-xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-[1.02] border border-white/20 animate-fade-in-up"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4 flex-1">
                <button className="mt-1 transition-transform hover:scale-110">{task.completed ? <CheckCircle2 className="h-6 w-6 text-emerald-500" /> : <Circle className="h-6 w-6 text-gray-400 hover:text-emerald-500" />}</button>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className={`text-lg font-semibold ${task.completed ? 'line-through text-gray-500' : 'text-gray-900'}`}>{task.title}</h3>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getPriorityColor(task.priority)}`}>{task.priority}</span>
                  </div>

                  <p className="text-gray-600 mb-3">{task.description}</p>

                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>{task.dueDate}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4" />
                      <span>{task.category}</span>
                    </div>
                  </div>
                </div>
              </div>

              <button className="p-2 rounded-xl hover:bg-gray-100 transition-colors opacity-0 group-hover:opacity-100">
                <MoreHorizontal className="h-5 w-5 text-gray-400" />
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Add Task Card */}
      <div className="rounded-3xl bg-gradient-to-br from-emerald-50 to-teal-50 p-8 border-2 border-dashed border-emerald-200 hover:border-emerald-300 transition-all duration-300 hover:scale-[1.02] cursor-pointer group">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto rounded-2xl bg-emerald-100 flex items-center justify-center group-hover:scale-110 transition-transform">
            <Plus className="h-8 w-8 text-emerald-600" />
          </div>
          <h3 className="mt-4 text-lg font-semibold text-gray-900">Add New Task</h3>
          <p className="mt-2 text-gray-600">Create a new task to stay organized</p>
        </div>
      </div>
    </div>
  );
};

export default Tasks;
