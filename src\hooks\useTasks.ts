// Custom hook for managing tasks

import { useState, useEffect, useCallback } from 'react';
import { Task, TaskFormData, TaskFilters, TaskSort } from '../types';
import { getTasks, saveTasks } from '../utils/storage';
import { generateId, getTaskPriorityWeight } from '../utils/helpers';

export const useTasks = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load tasks from storage on mount
  useEffect(() => {
    try {
      const storedTasks = getTasks();
      setTasks(storedTasks);
    } catch (err) {
      setError('Failed to load tasks');
      console.error('Error loading tasks:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Save tasks to storage whenever tasks change
  useEffect(() => {
    if (!loading) {
      try {
        saveTasks(tasks);
      } catch (err) {
        setError('Failed to save tasks');
        console.error('Error saving tasks:', err);
      }
    }
  }, [tasks, loading]);

  // Create a new task
  const createTask = useCallback((taskData: TaskFormData): Task => {
    const newTask: Task = {
      id: generateId(),
      title: taskData.title,
      description: taskData.description,
      completed: false,
      priority: taskData.priority,
      category: taskData.category,
      dueDate: taskData.dueDate ? new Date(taskData.dueDate) : undefined,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    setTasks(prev => [...prev, newTask]);
    return newTask;
  }, []);

  // Update an existing task
  const updateTask = useCallback((id: string, updates: Partial<Task>): void => {
    setTasks(prev => prev.map(task => 
      task.id === id 
        ? { ...task, ...updates, updatedAt: new Date() }
        : task
    ));
  }, []);

  // Delete a task
  const deleteTask = useCallback((id: string): void => {
    setTasks(prev => prev.filter(task => task.id !== id));
  }, []);

  // Toggle task completion
  const toggleTask = useCallback((id: string): void => {
    setTasks(prev => prev.map(task => 
      task.id === id 
        ? { ...task, completed: !task.completed, updatedAt: new Date() }
        : task
    ));
  }, []);

  // Get task by ID
  const getTask = useCallback((id: string): Task | undefined => {
    return tasks.find(task => task.id === id);
  }, [tasks]);

  // Filter tasks
  const filterTasks = useCallback((filters: TaskFilters): Task[] => {
    return tasks.filter(task => {
      // Category filter
      if (filters.category && task.category !== filters.category) {
        return false;
      }

      // Priority filter
      if (filters.priority && task.priority !== filters.priority) {
        return false;
      }

      // Completion filter
      if (filters.completed !== undefined && task.completed !== filters.completed) {
        return false;
      }

      // Due date range filter
      if (filters.dueDateRange) {
        if (!task.dueDate) return false;
        
        const { start, end } = filters.dueDateRange;
        if (start && task.dueDate < start) return false;
        if (end && task.dueDate > end) return false;
      }

      return true;
    });
  }, [tasks]);

  // Sort tasks
  const sortTasks = useCallback((tasksToSort: Task[], sort: TaskSort): Task[] => {
    return [...tasksToSort].sort((a, b) => {
      let comparison = 0;

      switch (sort.field) {
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'createdAt':
          comparison = a.createdAt.getTime() - b.createdAt.getTime();
          break;
        case 'dueDate':
          if (!a.dueDate && !b.dueDate) comparison = 0;
          else if (!a.dueDate) comparison = 1;
          else if (!b.dueDate) comparison = -1;
          else comparison = a.dueDate.getTime() - b.dueDate.getTime();
          break;
        case 'priority':
          comparison = getTaskPriorityWeight(b.priority) - getTaskPriorityWeight(a.priority);
          break;
        default:
          comparison = 0;
      }

      return sort.order === 'asc' ? comparison : -comparison;
    });
  }, []);

  // Get filtered and sorted tasks
  const getFilteredAndSortedTasks = useCallback((
    filters: TaskFilters = {},
    sort: TaskSort = { field: 'createdAt', order: 'desc' }
  ): Task[] => {
    const filtered = filterTasks(filters);
    return sortTasks(filtered, sort);
  }, [filterTasks, sortTasks]);

  // Get task statistics
  const getTaskStats = useCallback(() => {
    const total = tasks.length;
    const completed = tasks.filter(task => task.completed).length;
    const pending = total - completed;
    const overdue = tasks.filter(task => 
      task.dueDate && 
      !task.completed && 
      task.dueDate < new Date()
    ).length;

    const byPriority = {
      high: tasks.filter(task => task.priority === 'high' && !task.completed).length,
      medium: tasks.filter(task => task.priority === 'medium' && !task.completed).length,
      low: tasks.filter(task => task.priority === 'low' && !task.completed).length,
    };

    const byCategory = tasks.reduce((acc, task) => {
      acc[task.category] = (acc[task.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      completed,
      pending,
      overdue,
      byPriority,
      byCategory,
      completionRate: total > 0 ? Math.round((completed / total) * 100) : 0,
    };
  }, [tasks]);

  // Get unique categories
  const getCategories = useCallback((): string[] => {
    const categories = new Set(tasks.map(task => task.category));
    return Array.from(categories).sort();
  }, [tasks]);

  // Bulk operations
  const bulkUpdateTasks = useCallback((ids: string[], updates: Partial<Task>): void => {
    setTasks(prev => prev.map(task => 
      ids.includes(task.id)
        ? { ...task, ...updates, updatedAt: new Date() }
        : task
    ));
  }, []);

  const bulkDeleteTasks = useCallback((ids: string[]): void => {
    setTasks(prev => prev.filter(task => !ids.includes(task.id)));
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    tasks,
    loading,
    error,
    createTask,
    updateTask,
    deleteTask,
    toggleTask,
    getTask,
    filterTasks,
    sortTasks,
    getFilteredAndSortedTasks,
    getTaskStats,
    getCategories,
    bulkUpdateTasks,
    bulkDeleteTasks,
    clearError,
  };
};
