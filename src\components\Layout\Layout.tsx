import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import Sidebar from './Sidebar';
import Header from './Header';
import FloatingElements from '../ui/FloatingElements';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  const getPageTitle = (pathname: string): string => {
    switch (pathname) {
      case '/':
        return 'Dashboard';
      case '/tasks':
        return 'Tasks';
      case '/notes':
        return 'Notes';
      case '/goals':
        return 'Goals';
      case '/timer':
        return 'Pomodoro Timer';
      case '/settings':
        return 'Settings';
      default:
        return 'FocusNest';
    }
  };

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 overflow-hidden">
      {/* Enhanced Floating Elements */}
      <FloatingElements />

      <div className="relative z-10 flex h-screen">
        {/* Sidebar */}
        <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

        {/* Main content area */}
        <div className="flex-1 flex flex-col lg:ml-72 transition-all duration-300">
          {/* Header */}
          <Header title={getPageTitle(location.pathname)} onMenuClick={() => setSidebarOpen(true)} />

          {/* Main content with enhanced styling */}
          <main className="flex-1 overflow-y-auto p-4 lg:p-8 relative">
            <div className="max-w-7xl mx-auto relative z-10">
              <div className="animate-fade-in-up">{children}</div>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

export default Layout;
