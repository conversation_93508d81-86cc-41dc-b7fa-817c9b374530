# 🎯 FocusNest - Personal Productivity Manager

A modern, feature-rich productivity application built with React, TypeScript, and Tailwind CSS. FocusNest helps you manage tasks, take notes, track goals, and maintain focus with an integrated Pomodoro timer.

![FocusNest Dashboard](https://via.placeholder.com/800x400/3B82F6/FFFFFF?text=FocusNest+Dashboard)

## ✨ Features

### 📋 Task Management

- ✅ Create, edit, and delete tasks
- 🏷️ Organize with categories and priorities
- 📅 Set due dates and track deadlines
- 🔍 Filter and search functionality
- 📊 Task completion statistics

### 📝 Notes System

- 📄 Rich text note-taking
- 🏷️ Tag-based organization
- 🔍 Full-text search
- 📁 Category management
- 💾 Auto-save functionality

### 🎯 Goal Tracking

- 🎯 Set personal and professional goals
- 📈 Visual progress tracking
- 🏆 Milestone management
- 📊 Progress analytics
- ⏰ Deadline reminders

### ⏱️ Pomodoro Timer

- 🍅 Classic 25/5/15 minute intervals
- ⚙️ Customizable timer durations
- 🔔 Audio and visual notifications
- 📊 Session tracking and statistics
- 🎯 Task integration

### 📊 Dashboard & Analytics

- 📈 Productivity overview
- 📊 Daily/weekly/monthly stats
- 🏆 Achievement tracking
- 📅 Calendar integration
- 🎯 Goal progress visualization

## 🚀 Tech Stack

- **Frontend**: React 19 + TypeScript
- **Styling**: Tailwind CSS v4
- **Build Tool**: Vite
- **Routing**: React Router DOM
- **Icons**: Lucide React
- **Date Handling**: date-fns
- **Storage**: Local Storage
- **Deployment**: Vercel/Netlify Ready

## 🛠️ Installation & Setup

### Prerequisites

- Node.js 18+
- npm or yarn

### Quick Start

1. **Clone the repository**

   ```bash
   git clone https://github.com/yourusername/focusnest.git
   cd focusnest
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Start development server**

   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

### Build for Production

```bash
npm run build
npm run preview
```

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   └── Layout/         # Layout components (Header, Sidebar)
├── hooks/              # Custom React hooks
├── pages/              # Page components
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
│   ├── storage.ts      # Local storage management
│   └── helpers.ts      # Helper functions
├── App.tsx             # Main app component
└── main.tsx           # App entry point
```

## 🎨 Features Overview

### 🏠 Dashboard

- **Quick Stats**: View daily productivity metrics at a glance
- **Recent Activity**: See your latest tasks and accomplishments
- **Quick Actions**: Fast access to create tasks, notes, goals, and start timer
- **Progress Visualization**: Charts and progress bars for goals and tasks

### 📋 Task Management

- **Smart Organization**: Categories, priorities, and due dates
- **Advanced Filtering**: Filter by status, category, priority, and date ranges
- **Bulk Operations**: Select and modify multiple tasks at once
- **Task Statistics**: Completion rates and productivity insights

### 📝 Note Taking

- **Rich Text Editor**: Format your notes with markdown support
- **Tag System**: Organize notes with custom tags
- **Search & Filter**: Find notes quickly with full-text search
- **Categories**: Group related notes together

### 🎯 Goal Setting

- **SMART Goals**: Set specific, measurable, achievable goals
- **Progress Tracking**: Visual progress bars and percentage completion
- **Milestone System**: Break down large goals into smaller milestones
- **Deadline Management**: Set and track goal deadlines

### ⏱️ Pomodoro Timer

- **Customizable Sessions**: Adjust work and break durations
- **Session Tracking**: Monitor your focus sessions over time
- **Task Integration**: Link timer sessions to specific tasks
- **Statistics**: Daily, weekly, and monthly productivity reports

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the root directory:

```env
VITE_APP_NAME=FocusNest
VITE_APP_VERSION=1.0.0
```

### Customization

- **Themes**: Light, dark, and system themes available
- **Timer Settings**: Customize Pomodoro intervals
- **Notifications**: Configure desktop and sound notifications
- **Data Export**: Export your data in JSON format

## 📱 Responsive Design

FocusNest is fully responsive and works seamlessly across:

- 🖥️ Desktop computers
- 💻 Laptops and tablets
- 📱 Mobile devices

## 🚀 Deployment

### Deploy to Vercel

1. **Connect your repository to Vercel**
2. **Configure build settings**:
   - Build Command: `npm run build`
   - Output Directory: `dist`
3. **Deploy**: Automatic deployments on every push

### Deploy to Netlify

1. **Connect your repository to Netlify**
2. **Configure build settings**:
   - Build Command: `npm run build`
   - Publish Directory: `dist`
3. **Deploy**: Automatic deployments on every push

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes
4. Run tests: `npm run test`
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **React Team** for the amazing framework
- **Tailwind CSS** for the utility-first CSS framework
- **Lucide** for the beautiful icons
- **Vite** for the lightning-fast build tool

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/yourusername/focusnest/issues)
- 💬 Discussions: [GitHub Discussions](https://github.com/yourusername/focusnest/discussions)

---

**Made with ❤️ by [Your Name](https://github.com/yourusername)**

⭐ If you found this project helpful, please give it a star on GitHub!
