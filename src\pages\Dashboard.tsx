import React from 'react';
import { CheckSquare, FileText, Target, Timer, TrendingUp, Calendar, Clock, Award, Zap, Star, Activity, BarChart3 } from 'lucide-react';

const Dashboard: React.FC = () => {
  // Mock data - will be replaced with real data from hooks
  const stats = {
    tasksCompleted: 12,
    totalTasks: 18,
    focusTime: 180, // minutes
    pomodoroSessions: 7,
    notesCreated: 5,
    goalsProgress: 75,
  };

  const recentTasks = [
    { id: '1', title: 'Complete project proposal', completed: true },
    { id: '2', title: 'Review design mockups', completed: false },
    { id: '3', title: 'Schedule team meeting', completed: true },
  ];

  const upcomingTasks = [
    { id: '4', title: 'Submit quarterly report', dueDate: 'Tomorrow' },
    { id: '5', title: 'Client presentation', dueDate: 'Friday' },
  ];

  return (
    <div className="space-y-8 p-1">
      {/* Modern Welcome Section with Glass Effect */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-white/10 blur-xl"></div>
        <div className="absolute -bottom-8 -left-8 h-32 w-32 rounded-full bg-white/5 blur-2xl"></div>

        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-3xl font-bold mb-2 text-gradient">Welcome back! 🚀</h2>
              <p className="text-indigo-100 text-lg">
                You've completed <span className="font-semibold text-white">{stats.tasksCompleted}</span> tasks today.
                <span className="block mt-1">Keep up the amazing work!</span>
              </p>
            </div>
            <div className="hidden md:block">
              <div className="float">
                <div className="w-20 h-20 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center">
                  <Zap className="h-10 w-10 text-yellow-300" />
                </div>
              </div>
            </div>
          </div>

          {/* Progress Ring */}
          <div className="mt-6 flex items-center space-x-4">
            <div className="relative w-16 h-16">
              <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 64 64">
                <circle cx="32" cy="32" r="28" stroke="currentColor" strokeWidth="4" fill="none" className="text-white/20" />
                <circle
                  cx="32"
                  cy="32"
                  r="28"
                  stroke="currentColor"
                  strokeWidth="4"
                  fill="none"
                  strokeDasharray={`${2 * Math.PI * 28}`}
                  strokeDashoffset={`${2 * Math.PI * 28 * (1 - stats.tasksCompleted / stats.totalTasks)}`}
                  className="text-white"
                  strokeLinecap="round"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-sm font-bold text-white">{Math.round((stats.tasksCompleted / stats.totalTasks) * 100)}%</span>
              </div>
            </div>
            <div>
              <p className="text-white/90 text-sm">Daily Progress</p>
              <p className="text-white font-semibold">
                {stats.tasksCompleted} of {stats.totalTasks} tasks
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Tasks Stats - Modern Glass Card */}
        <div className="group relative overflow-hidden rounded-2xl bg-white/80 backdrop-blur-sm border border-white/20 p-6 shadow-xl hover:shadow-2xl transition-all duration-300 card-hover">
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-50 to-green-100 opacity-50"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl shadow-lg">
                <CheckSquare className="h-6 w-6 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">
                  {stats.tasksCompleted}
                  <span className="text-lg text-gray-500">/{stats.totalTasks}</span>
                </div>
                <p className="text-sm font-medium text-gray-600">Tasks Completed</p>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Progress</span>
                <span className="font-semibold text-emerald-600">{Math.round((stats.tasksCompleted / stats.totalTasks) * 100)}%</span>
              </div>
              <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                <div className="h-full bg-gradient-to-r from-emerald-500 to-green-600 rounded-full transition-all duration-500 ease-out" style={{ width: `${(stats.tasksCompleted / stats.totalTasks) * 100}%` }}></div>
              </div>
            </div>
          </div>
        </div>

        {/* Focus Time - Modern Glass Card */}
        <div className="group relative overflow-hidden rounded-2xl bg-white/80 backdrop-blur-sm border border-white/20 p-6 shadow-xl hover:shadow-2xl transition-all duration-300 card-hover">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-100 opacity-50"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg">
                <Clock className="h-6 w-6 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">
                  {Math.floor(stats.focusTime / 60)}h {stats.focusTime % 60}m
                </div>
                <p className="text-sm font-medium text-gray-600">Focus Time</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Timer className="h-4 w-4 text-blue-500" />
              <span className="text-sm text-gray-600">{stats.pomodoroSessions} Pomodoro sessions</span>
            </div>
          </div>
        </div>

        {/* Notes - Modern Glass Card */}
        <div className="group relative overflow-hidden rounded-2xl bg-white/80 backdrop-blur-sm border border-white/20 p-6 shadow-xl hover:shadow-2xl transition-all duration-300 card-hover">
          <div className="absolute inset-0 bg-gradient-to-br from-amber-50 to-yellow-100 opacity-50"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-br from-amber-500 to-yellow-600 rounded-xl shadow-lg">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">{stats.notesCreated}</div>
                <p className="text-sm font-medium text-gray-600">Notes Created</p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Star className="h-4 w-4 text-amber-500" />
              <span className="text-sm text-gray-600">Created today</span>
            </div>
          </div>
        </div>

        {/* Goals Progress - Modern Glass Card */}
        <div className="group relative overflow-hidden rounded-2xl bg-white/80 backdrop-blur-sm border border-white/20 p-6 shadow-xl hover:shadow-2xl transition-all duration-300 card-hover">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-50 to-violet-100 opacity-50"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-violet-600 rounded-xl shadow-lg">
                <Target className="h-6 w-6 text-white" />
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-gray-900">{stats.goalsProgress}%</div>
                <p className="text-sm font-medium text-gray-600">Goals Progress</p>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Achievement</span>
                <span className="font-semibold text-purple-600">{stats.goalsProgress}%</span>
              </div>
              <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                <div className="h-full bg-gradient-to-r from-purple-500 to-violet-600 rounded-full transition-all duration-500 ease-out" style={{ width: `${stats.goalsProgress}%` }}></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Activity Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Tasks - Modern Glass Card */}
        <div className="relative overflow-hidden rounded-2xl bg-white/80 backdrop-blur-sm border border-white/20 p-6 shadow-xl">
          <div className="absolute inset-0 bg-gradient-to-br from-slate-50 to-gray-100 opacity-50"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900">Recent Tasks</h3>
              <Activity className="h-5 w-5 text-gray-500" />
            </div>

            <div className="space-y-4">
              {recentTasks.map((task, index) => (
                <div key={task.id} className="group flex items-center p-3 rounded-xl bg-white/50 hover:bg-white/80 transition-all duration-200">
                  <div
                    className={`w-5 h-5 rounded-full border-2 mr-4 flex items-center justify-center transition-all duration-200 ${
                      task.completed ? 'bg-emerald-500 border-emerald-500 shadow-lg shadow-emerald-200' : 'border-gray-300 hover:border-emerald-400'
                    }`}
                  >
                    {task.completed && <CheckSquare className="w-3 h-3 text-white" />}
                  </div>
                  <span className={`text-sm font-medium transition-all duration-200 ${task.completed ? 'text-gray-500 line-through' : 'text-gray-900 group-hover:text-gray-700'}`}>{task.title}</span>
                </div>
              ))}
            </div>

            <button className="mt-6 w-full btn-modern bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 px-4 rounded-xl font-medium hover:shadow-lg transition-all duration-300">View all tasks →</button>
          </div>
        </div>

        {/* Upcoming Tasks - Modern Glass Card */}
        <div className="relative overflow-hidden rounded-2xl bg-white/80 backdrop-blur-sm border border-white/20 p-6 shadow-xl">
          <div className="absolute inset-0 bg-gradient-to-br from-orange-50 to-red-100 opacity-50"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900">Upcoming</h3>
              <Calendar className="h-5 w-5 text-gray-500" />
            </div>

            <div className="space-y-4">
              {upcomingTasks.map((task, index) => (
                <div key={task.id} className="group flex items-center justify-between p-3 rounded-xl bg-white/50 hover:bg-white/80 transition-all duration-200">
                  <span className="text-sm font-medium text-gray-900 group-hover:text-gray-700">{task.title}</span>
                  <span className="text-xs font-semibold text-orange-600 bg-orange-100 px-3 py-1 rounded-full">{task.dueDate}</span>
                </div>
              ))}
            </div>

            <button className="mt-6 w-full btn-modern bg-gradient-to-r from-orange-500 to-red-600 text-white py-3 px-4 rounded-xl font-medium hover:shadow-lg transition-all duration-300">View calendar →</button>
          </div>
        </div>
      </div>

      {/* Modern Quick Actions */}
      <div className="relative overflow-hidden rounded-2xl bg-white/80 backdrop-blur-sm border border-white/20 p-8 shadow-xl">
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 to-purple-100 opacity-50"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-8">
            <h3 className="text-2xl font-bold text-gray-900">Quick Actions</h3>
            <BarChart3 className="h-6 w-6 text-gray-500" />
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <button className="group relative overflow-hidden btn-modern flex flex-col items-center p-6 bg-gradient-to-br from-emerald-500 to-green-600 text-white rounded-2xl hover:shadow-2xl hover:shadow-emerald-200 transition-all duration-300 transform hover:scale-105">
              <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <CheckSquare className="h-8 w-8 mb-3 relative z-10" />
              <span className="text-sm font-semibold relative z-10">Add Task</span>
            </button>

            <button className="group relative overflow-hidden btn-modern flex flex-col items-center p-6 bg-gradient-to-br from-amber-500 to-yellow-600 text-white rounded-2xl hover:shadow-2xl hover:shadow-amber-200 transition-all duration-300 transform hover:scale-105">
              <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <FileText className="h-8 w-8 mb-3 relative z-10" />
              <span className="text-sm font-semibold relative z-10">New Note</span>
            </button>

            <button className="group relative overflow-hidden btn-modern flex flex-col items-center p-6 bg-gradient-to-br from-purple-500 to-violet-600 text-white rounded-2xl hover:shadow-2xl hover:shadow-purple-200 transition-all duration-300 transform hover:scale-105">
              <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <Target className="h-8 w-8 mb-3 relative z-10" />
              <span className="text-sm font-semibold relative z-10">Set Goal</span>
            </button>

            <button className="group relative overflow-hidden btn-modern flex flex-col items-center p-6 bg-gradient-to-br from-blue-500 to-indigo-600 text-white rounded-2xl hover:shadow-2xl hover:shadow-blue-200 transition-all duration-300 transform hover:scale-105">
              <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <Timer className="h-8 w-8 mb-3 relative z-10" />
              <span className="text-sm font-semibold relative z-10">Start Timer</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
