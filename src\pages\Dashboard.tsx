import React from 'react';
import { 
  CheckSquare, 
  FileText, 
  Target, 
  Timer, 
  TrendingUp,
  Calendar,
  Clock,
  Award
} from 'lucide-react';

const Dashboard: React.FC = () => {
  // Mock data - will be replaced with real data from hooks
  const stats = {
    tasksCompleted: 12,
    totalTasks: 18,
    focusTime: 180, // minutes
    pomodoroSessions: 7,
    notesCreated: 5,
    goalsProgress: 75,
  };

  const recentTasks = [
    { id: '1', title: 'Complete project proposal', completed: true },
    { id: '2', title: 'Review design mockups', completed: false },
    { id: '3', title: 'Schedule team meeting', completed: true },
  ];

  const upcomingTasks = [
    { id: '4', title: 'Submit quarterly report', dueDate: 'Tomorrow' },
    { id: '5', title: 'Client presentation', dueDate: 'Friday' },
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg p-6 text-white">
        <h2 className="text-2xl font-bold mb-2">Welcome back!</h2>
        <p className="text-blue-100">
          You've completed {stats.tasksCompleted} tasks today. Keep up the great work!
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Tasks Stats */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckSquare className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Tasks</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats.tasksCompleted}/{stats.totalTasks}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <div className="flex items-center">
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full" 
                  style={{ width: `${(stats.tasksCompleted / stats.totalTasks) * 100}%` }}
                ></div>
              </div>
              <span className="ml-2 text-sm text-gray-600">
                {Math.round((stats.tasksCompleted / stats.totalTasks) * 100)}%
              </span>
            </div>
          </div>
        </div>

        {/* Focus Time */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Clock className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Focus Time</p>
              <p className="text-2xl font-semibold text-gray-900">
                {Math.floor(stats.focusTime / 60)}h {stats.focusTime % 60}m
              </p>
            </div>
          </div>
          <div className="mt-4">
            <p className="text-sm text-gray-600">
              {stats.pomodoroSessions} Pomodoro sessions
            </p>
          </div>
        </div>

        {/* Notes */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <FileText className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Notes</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats.notesCreated}
              </p>
            </div>
          </div>
          <div className="mt-4">
            <p className="text-sm text-gray-600">Created today</p>
          </div>
        </div>

        {/* Goals Progress */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Target className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Goals</p>
              <p className="text-2xl font-semibold text-gray-900">
                {stats.goalsProgress}%
              </p>
            </div>
          </div>
          <div className="mt-4">
            <div className="flex items-center">
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-purple-600 h-2 rounded-full" 
                  style={{ width: `${stats.goalsProgress}%` }}
                ></div>
              </div>
              <span className="ml-2 text-sm text-gray-600">
                {stats.goalsProgress}%
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity and Upcoming Tasks */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Tasks */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Tasks</h3>
          <div className="space-y-3">
            {recentTasks.map((task) => (
              <div key={task.id} className="flex items-center">
                <div className={`w-4 h-4 rounded border-2 mr-3 ${
                  task.completed 
                    ? 'bg-green-500 border-green-500' 
                    : 'border-gray-300'
                }`}>
                  {task.completed && (
                    <CheckSquare className="w-3 h-3 text-white" />
                  )}
                </div>
                <span className={`text-sm ${
                  task.completed 
                    ? 'text-gray-500 line-through' 
                    : 'text-gray-900'
                }`}>
                  {task.title}
                </span>
              </div>
            ))}
          </div>
          <button className="mt-4 text-sm text-blue-600 hover:text-blue-700 font-medium">
            View all tasks →
          </button>
        </div>

        {/* Upcoming Tasks */}
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Upcoming</h3>
          <div className="space-y-3">
            {upcomingTasks.map((task) => (
              <div key={task.id} className="flex items-center justify-between">
                <span className="text-sm text-gray-900">{task.title}</span>
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                  {task.dueDate}
                </span>
              </div>
            ))}
          </div>
          <button className="mt-4 text-sm text-blue-600 hover:text-blue-700 font-medium">
            View calendar →
          </button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <CheckSquare className="h-6 w-6 text-blue-600 mb-2" />
            <span className="text-sm font-medium text-gray-900">Add Task</span>
          </button>
          <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <FileText className="h-6 w-6 text-blue-600 mb-2" />
            <span className="text-sm font-medium text-gray-900">New Note</span>
          </button>
          <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Target className="h-6 w-6 text-blue-600 mb-2" />
            <span className="text-sm font-medium text-gray-900">Set Goal</span>
          </button>
          <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <Timer className="h-6 w-6 text-blue-600 mb-2" />
            <span className="text-sm font-medium text-gray-900">Start Timer</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
