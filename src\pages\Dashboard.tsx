import React from 'react';
import { format } from 'date-fns';
import { CheckSquare, Clock, Target, TrendingUp, Calendar, Zap, Plus, ArrowRight, Sparkles, Star, Award, Activity, BarChart3, Timer, FileText } from 'lucide-react';

const Dashboard: React.FC = () => {
  // Mock data - will be replaced with real data from hooks
  const stats = {
    tasksCompleted: 12,
    totalTasks: 18,
    focusTime: 180, // minutes
    pomodoroSessions: 7,
    notesCreated: 5,
    goalsProgress: 75,
  };

  const recentTasks = [
    { id: '1', title: 'Complete project proposal', completed: true },
    { id: '2', title: 'Review design mockups', completed: false },
    { id: '3', title: 'Schedule team meeting', completed: true },
  ];

  const upcomingTasks = [
    { id: '4', title: 'Submit quarterly report', dueDate: 'Tomorrow' },
    { id: '5', title: 'Client presentation', dueDate: 'Friday' },
  ];

  return (
    <div className="space-y-8 animate-fade-in-up">
      {/* Hero Section with Enhanced Design */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 p-8 text-white">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-white/10 blur-xl animate-pulse"></div>
        <div className="absolute -bottom-4 -left-4 h-32 w-32 rounded-full bg-white/5 blur-2xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 right-1/4 h-16 w-16 rounded-full bg-white/5 blur-xl animate-float"></div>

        {/* Geometric Decorations */}
        <div className="absolute top-4 right-8 w-8 h-8 border-2 border-white/20 rounded-lg rotate-45 animate-spin-slow"></div>
        <div className="absolute bottom-8 right-12 w-6 h-6 bg-white/10 rounded-full animate-bounce-slow"></div>

        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center animate-glow">
                  <Zap className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-4xl font-bold">Welcome back! 👋</h1>
                  <p className="text-xl text-white/90 mt-1">Ready to conquer your goals today?</p>
                </div>
              </div>
            </div>

            {/* Floating Action Cards */}
            <div className="hidden lg:flex space-x-4">
              <div className="w-20 h-20 rounded-2xl bg-white/10 backdrop-blur-sm flex items-center justify-center animate-float hover:scale-110 transition-transform cursor-pointer">
                <Target className="h-8 w-8 text-white" />
              </div>
              <div className="w-20 h-20 rounded-2xl bg-white/10 backdrop-blur-sm flex items-center justify-center animate-float delay-300 hover:scale-110 transition-transform cursor-pointer">
                <TrendingUp className="h-8 w-8 text-white" />
              </div>
            </div>
          </div>

          {/* Enhanced Info Cards */}
          <div className="mt-8 flex flex-wrap gap-4">
            <div className="flex items-center space-x-3 bg-white/15 backdrop-blur-sm rounded-2xl px-6 py-3 hover:bg-white/20 transition-all duration-300 group">
              <Calendar className="h-5 w-5 group-hover:scale-110 transition-transform" />
              <span className="font-semibold">{format(new Date(), 'EEEE, MMMM do')}</span>
            </div>
            <div className="flex items-center space-x-3 bg-white/15 backdrop-blur-sm rounded-2xl px-6 py-3 hover:bg-white/20 transition-all duration-300 group">
              <Clock className="h-5 w-5 group-hover:scale-110 transition-transform" />
              <span className="font-semibold">{format(new Date(), 'h:mm a')}</span>
            </div>
            <div className="flex items-center space-x-3 bg-white/15 backdrop-blur-sm rounded-2xl px-6 py-3 hover:bg-white/20 transition-all duration-300 group">
              <Sparkles className="h-5 w-5 group-hover:scale-110 transition-transform" />
              <span className="font-semibold">Productive Mode</span>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Tasks Card */}
        <div className="group relative overflow-hidden rounded-3xl bg-white/80 backdrop-blur-xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border border-white/20">
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 to-green-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 rounded-2xl bg-emerald-100 flex items-center justify-center group-hover:scale-110 transition-transform">
                <CheckSquare className="h-6 w-6 text-emerald-600" />
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-gray-900">{stats.tasksCompleted}</p>
                <p className="text-sm text-gray-500">of {stats.totalTasks}</p>
              </div>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Tasks Completed</h3>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-gradient-to-r from-emerald-500 to-green-600 h-2 rounded-full transition-all duration-500" style={{ width: `${(stats.tasksCompleted / stats.totalTasks) * 100}%` }}></div>
            </div>
          </div>
        </div>

        {/* Focus Time Card */}
        <div className="group relative overflow-hidden rounded-3xl bg-white/80 backdrop-blur-xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border border-white/20">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-indigo-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 rounded-2xl bg-blue-100 flex items-center justify-center group-hover:scale-110 transition-transform">
                <Timer className="h-6 w-6 text-blue-600" />
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-gray-900">{Math.floor(stats.focusTime / 60)}h</p>
                <p className="text-sm text-gray-500">{stats.focusTime % 60}m</p>
              </div>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Focus Time</h3>
            <p className="text-sm text-gray-600">{stats.pomodoroSessions} Pomodoro sessions</p>
          </div>
        </div>

        {/* Goals Card */}
        <div className="group relative overflow-hidden rounded-3xl bg-white/80 backdrop-blur-xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border border-white/20">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-violet-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 rounded-2xl bg-purple-100 flex items-center justify-center group-hover:scale-110 transition-transform">
                <Target className="h-6 w-6 text-purple-600" />
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-gray-900">{stats.goalsProgress}%</p>
                <p className="text-sm text-gray-500">Progress</p>
              </div>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Goals Progress</h3>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-gradient-to-r from-purple-500 to-violet-600 h-2 rounded-full transition-all duration-500" style={{ width: `${stats.goalsProgress}%` }}></div>
            </div>
          </div>
        </div>

        {/* Notes Card */}
        <div className="group relative overflow-hidden rounded-3xl bg-white/80 backdrop-blur-xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border border-white/20">
          <div className="absolute inset-0 bg-gradient-to-br from-amber-500/10 to-orange-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 rounded-2xl bg-amber-100 flex items-center justify-center group-hover:scale-110 transition-transform">
                <FileText className="h-6 w-6 text-amber-600" />
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-gray-900">{stats.notesCreated}</p>
                <p className="text-sm text-gray-500">Notes</p>
              </div>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">Notes Created</h3>
            <p className="text-sm text-gray-600">This week</p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Tasks */}
        <div className="rounded-3xl bg-white/80 backdrop-blur-xl p-6 shadow-xl border border-white/20">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">Recent Tasks</h2>
            <button className="flex items-center space-x-2 text-indigo-600 hover:text-indigo-700 font-medium">
              <span>View all</span>
              <ArrowRight className="h-4 w-4" />
            </button>
          </div>
          <div className="space-y-3">
            {recentTasks.map((task) => (
              <div key={task.id} className="flex items-center space-x-3 p-3 rounded-2xl hover:bg-gray-50 transition-colors">
                <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${task.completed ? 'bg-emerald-500 border-emerald-500' : 'border-gray-300'}`}>
                  {task.completed && <CheckSquare className="h-3 w-3 text-white" />}
                </div>
                <span className={`flex-1 ${task.completed ? 'line-through text-gray-500' : 'text-gray-900'}`}>{task.title}</span>
              </div>
            ))}
          </div>
          <button className="w-full mt-4 flex items-center justify-center space-x-2 p-3 rounded-2xl bg-indigo-50 text-indigo-600 hover:bg-indigo-100 transition-colors">
            <Plus className="h-4 w-4" />
            <span>Add new task</span>
          </button>
        </div>

        {/* Upcoming Tasks */}
        <div className="rounded-3xl bg-white/80 backdrop-blur-xl p-6 shadow-xl border border-white/20">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">Upcoming</h2>
            <button className="flex items-center space-x-2 text-indigo-600 hover:text-indigo-700 font-medium">
              <span>View calendar</span>
              <ArrowRight className="h-4 w-4" />
            </button>
          </div>
          <div className="space-y-3">
            {upcomingTasks.map((task) => (
              <div key={task.id} className="flex items-center justify-between p-3 rounded-2xl hover:bg-gray-50 transition-colors">
                <span className="text-gray-900">{task.title}</span>
                <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">{task.dueDate}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
