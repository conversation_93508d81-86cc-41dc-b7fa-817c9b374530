import React from 'react';
import { Bell, Moon, <PERSON>, Monitor, Volume2, VolumeX } from 'lucide-react';

const Settings: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Settings</h2>
        <p className="mt-1 text-sm text-gray-600">
          Customize your FocusNest experience
        </p>
      </div>

      {/* General Settings */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">General</h3>
        </div>
        <div className="p-6 space-y-6">
          {/* Theme Setting */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-900">Theme</label>
              <p className="text-sm text-gray-600">Choose your preferred theme</p>
            </div>
            <div className="flex items-center space-x-2">
              <button className="p-2 rounded-md bg-gray-100 text-gray-600">
                <Sun className="h-4 w-4" />
              </button>
              <button className="p-2 rounded-md bg-blue-600 text-white">
                <Monitor className="h-4 w-4" />
              </button>
              <button className="p-2 rounded-md bg-gray-100 text-gray-600">
                <Moon className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Notifications */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-900">Notifications</label>
              <p className="text-sm text-gray-600">Enable desktop notifications</p>
            </div>
            <button className="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-blue-600 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              <span className="translate-x-5 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
            </button>
          </div>
        </div>
      </div>

      {/* Pomodoro Settings */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Pomodoro Timer</h3>
        </div>
        <div className="p-6 space-y-6">
          {/* Work Duration */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-900">Work Duration</label>
              <p className="text-sm text-gray-600">Length of focus sessions</p>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="number"
                min="1"
                max="60"
                defaultValue="25"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
              <span className="text-sm text-gray-600">min</span>
            </div>
          </div>

          {/* Short Break Duration */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-900">Short Break</label>
              <p className="text-sm text-gray-600">Length of short breaks</p>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="number"
                min="1"
                max="30"
                defaultValue="5"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
              <span className="text-sm text-gray-600">min</span>
            </div>
          </div>

          {/* Long Break Duration */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-900">Long Break</label>
              <p className="text-sm text-gray-600">Length of long breaks</p>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="number"
                min="1"
                max="60"
                defaultValue="15"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
              <span className="text-sm text-gray-600">min</span>
            </div>
          </div>

          {/* Sessions until long break */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-900">Long Break Interval</label>
              <p className="text-sm text-gray-600">Sessions before long break</p>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="number"
                min="2"
                max="10"
                defaultValue="4"
                className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
              <span className="text-sm text-gray-600">sessions</span>
            </div>
          </div>

          {/* Auto-start breaks */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-900">Auto-start Breaks</label>
              <p className="text-sm text-gray-600">Automatically start break timers</p>
            </div>
            <button className="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              <span className="translate-x-0 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
            </button>
          </div>

          {/* Sound notifications */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-900">Sound Notifications</label>
              <p className="text-sm text-gray-600">Play sound when timer ends</p>
            </div>
            <button className="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-blue-600 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              <span className="translate-x-5 inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"></span>
            </button>
          </div>
        </div>
      </div>

      {/* Data Management */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Data Management</h3>
        </div>
        <div className="p-6 space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-900">Export Data</label>
              <p className="text-sm text-gray-600">Download your data as JSON</p>
            </div>
            <button className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100">
              Export
            </button>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-900">Clear All Data</label>
              <p className="text-sm text-gray-600">Permanently delete all your data</p>
            </div>
            <button className="px-4 py-2 text-sm font-medium text-red-600 bg-red-50 border border-red-200 rounded-md hover:bg-red-100">
              Clear Data
            </button>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button className="px-6 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          Save Changes
        </button>
      </div>
    </div>
  );
};

export default Settings;
