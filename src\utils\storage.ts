// Local storage utilities for FocusNest application

import { Task, Note, Goal, PomodoroSession, AppSettings, DailyStats } from '../types';

// Storage keys
const STORAGE_KEYS = {
  TASKS: 'focusnest_tasks',
  NOTES: 'focusnest_notes',
  GOALS: 'focusnest_goals',
  POMODORO_SESSIONS: 'focusnest_pomodoro_sessions',
  SETTINGS: 'focusnest_settings',
  DAILY_STATS: 'focusnest_daily_stats',
} as const;

// Generic storage functions
export const getFromStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key);
    if (!item) return defaultValue;
    
    const parsed = JSON.parse(item);
    
    // Convert date strings back to Date objects for specific types
    if (key === STORAGE_KEYS.TASKS) {
      return (parsed as Task[]).map(task => ({
        ...task,
        dueDate: task.dueDate ? new Date(task.dueDate) : undefined,
        createdAt: new Date(task.createdAt),
        updatedAt: new Date(task.updatedAt),
      })) as T;
    }
    
    if (key === STORAGE_KEYS.NOTES) {
      return (parsed as Note[]).map(note => ({
        ...note,
        createdAt: new Date(note.createdAt),
        updatedAt: new Date(note.updatedAt),
      })) as T;
    }
    
    if (key === STORAGE_KEYS.GOALS) {
      return (parsed as Goal[]).map(goal => ({
        ...goal,
        deadline: goal.deadline ? new Date(goal.deadline) : undefined,
        createdAt: new Date(goal.createdAt),
        updatedAt: new Date(goal.updatedAt),
      })) as T;
    }
    
    if (key === STORAGE_KEYS.POMODORO_SESSIONS) {
      return (parsed as PomodoroSession[]).map(session => ({
        ...session,
        startTime: new Date(session.startTime),
        endTime: session.endTime ? new Date(session.endTime) : undefined,
      })) as T;
    }
    
    return parsed;
  } catch (error) {
    console.error(`Error reading from localStorage for key ${key}:`, error);
    return defaultValue;
  }
};

export const saveToStorage = <T>(key: string, value: T): void => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
    console.error(`Error saving to localStorage for key ${key}:`, error);
  }
};

export const removeFromStorage = (key: string): void => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error(`Error removing from localStorage for key ${key}:`, error);
  }
};

// Specific storage functions for each data type
export const getTasks = (): Task[] => {
  return getFromStorage(STORAGE_KEYS.TASKS, []);
};

export const saveTasks = (tasks: Task[]): void => {
  saveToStorage(STORAGE_KEYS.TASKS, tasks);
};

export const getNotes = (): Note[] => {
  return getFromStorage(STORAGE_KEYS.NOTES, []);
};

export const saveNotes = (notes: Note[]): void => {
  saveToStorage(STORAGE_KEYS.NOTES, notes);
};

export const getGoals = (): Goal[] => {
  return getFromStorage(STORAGE_KEYS.GOALS, []);
};

export const saveGoals = (goals: Goal[]): void => {
  saveToStorage(STORAGE_KEYS.GOALS, goals);
};

export const getPomodoroSessions = (): PomodoroSession[] => {
  return getFromStorage(STORAGE_KEYS.POMODORO_SESSIONS, []);
};

export const savePomodoroSessions = (sessions: PomodoroSession[]): void => {
  saveToStorage(STORAGE_KEYS.POMODORO_SESSIONS, sessions);
};

export const getSettings = (): AppSettings => {
  const defaultSettings: AppSettings = {
    theme: 'system',
    notifications: true,
    pomodoroSettings: {
      workDuration: 25,
      shortBreakDuration: 5,
      longBreakDuration: 15,
      sessionsUntilLongBreak: 4,
      autoStartBreaks: false,
      autoStartPomodoros: false,
      soundEnabled: true,
    },
  };
  
  return getFromStorage(STORAGE_KEYS.SETTINGS, defaultSettings);
};

export const saveSettings = (settings: AppSettings): void => {
  saveToStorage(STORAGE_KEYS.SETTINGS, settings);
};

export const getDailyStats = (): DailyStats[] => {
  return getFromStorage(STORAGE_KEYS.DAILY_STATS, []);
};

export const saveDailyStats = (stats: DailyStats[]): void => {
  saveToStorage(STORAGE_KEYS.DAILY_STATS, stats);
};

// Utility function to clear all data (for development/testing)
export const clearAllData = (): void => {
  Object.values(STORAGE_KEYS).forEach(key => {
    removeFromStorage(key);
  });
};

// Export storage keys for use in other modules
export { STORAGE_KEYS };
