// Helper utility functions for FocusNest application

import { format, isToday, isTomorrow, isYesterday, differenceInDays, startOfDay } from 'date-fns';
import { v4 as uuidv4 } from 'uuid';
import { Task, Goal, PomodoroSession, DailyStats } from '../types';

// ID generation
export const generateId = (): string => uuidv4();

// Date formatting utilities
export const formatDate = (date: Date, formatString: string = 'MMM dd, yyyy'): string => {
  return format(date, formatString);
};

export const formatTime = (date: Date): string => {
  return format(date, 'HH:mm');
};

export const formatDateTime = (date: Date): string => {
  return format(date, 'MMM dd, yyyy HH:mm');
};

export const getRelativeDate = (date: Date): string => {
  if (isToday(date)) return 'Today';
  if (isTomorrow(date)) return 'Tomorrow';
  if (isYesterday(date)) return 'Yesterday';
  
  const daysDiff = differenceInDays(date, new Date());
  if (daysDiff > 0) {
    return `In ${daysDiff} day${daysDiff > 1 ? 's' : ''}`;
  } else {
    return `${Math.abs(daysDiff)} day${Math.abs(daysDiff) > 1 ? 's' : ''} ago`;
  }
};

export const isOverdue = (date: Date): boolean => {
  return differenceInDays(startOfDay(new Date()), startOfDay(date)) > 0;
};

// Time conversion utilities
export const minutesToHours = (minutes: number): string => {
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (hours === 0) {
    return `${remainingMinutes}m`;
  } else if (remainingMinutes === 0) {
    return `${hours}h`;
  } else {
    return `${hours}h ${remainingMinutes}m`;
  }
};

export const secondsToMinutesAndSeconds = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// Task utilities
export const getTaskPriorityColor = (priority: Task['priority']): string => {
  switch (priority) {
    case 'high':
      return 'text-red-600 bg-red-50 border-red-200';
    case 'medium':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'low':
      return 'text-green-600 bg-green-50 border-green-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
};

export const getTaskPriorityWeight = (priority: Task['priority']): number => {
  switch (priority) {
    case 'high': return 3;
    case 'medium': return 2;
    case 'low': return 1;
    default: return 0;
  }
};

// Goal utilities
export const calculateGoalProgress = (goal: Goal): number => {
  if (goal.targetValue === 0) return 0;
  return Math.min((goal.currentValue / goal.targetValue) * 100, 100);
};

export const getGoalStatusColor = (goal: Goal): string => {
  const progress = calculateGoalProgress(goal);
  
  if (goal.completed) {
    return 'text-green-600 bg-green-50 border-green-200';
  } else if (progress >= 75) {
    return 'text-blue-600 bg-blue-50 border-blue-200';
  } else if (progress >= 50) {
    return 'text-yellow-600 bg-yellow-50 border-yellow-200';
  } else {
    return 'text-gray-600 bg-gray-50 border-gray-200';
  }
};

// Statistics utilities
export const calculateDailyStats = (
  tasks: Task[],
  sessions: PomodoroSession[],
  goals: Goal[],
  date: Date
): DailyStats => {
  const dateString = format(date, 'yyyy-MM-dd');
  const startOfTargetDay = startOfDay(date);
  const endOfTargetDay = new Date(startOfTargetDay);
  endOfTargetDay.setDate(endOfTargetDay.getDate() + 1);

  // Tasks completed on this date
  const tasksCompleted = tasks.filter(task => 
    task.completed && 
    task.updatedAt >= startOfTargetDay && 
    task.updatedAt < endOfTargetDay
  ).length;

  // Pomodoro sessions on this date
  const dailySessions = sessions.filter(session =>
    session.startTime >= startOfTargetDay && 
    session.startTime < endOfTargetDay &&
    session.completed
  );

  const totalFocusTime = dailySessions
    .filter(session => session.type === 'work')
    .reduce((total, session) => total + session.duration, 0);

  const pomodoroSessions = dailySessions.filter(session => session.type === 'work').length;

  // Notes created (would need to be passed as parameter if tracking this)
  const notesCreated = 0; // Placeholder

  // Goals progress (average progress of all active goals)
  const activeGoals = goals.filter(goal => !goal.completed);
  const goalsProgress = activeGoals.length > 0 
    ? activeGoals.reduce((sum, goal) => sum + calculateGoalProgress(goal), 0) / activeGoals.length
    : 0;

  return {
    date: dateString,
    tasksCompleted,
    totalFocusTime,
    pomodoroSessions,
    notesCreated,
    goalsProgress: Math.round(goalsProgress),
  };
};

// Array utilities
export const sortByDate = <T extends { createdAt: Date }>(items: T[], order: 'asc' | 'desc' = 'desc'): T[] => {
  return [...items].sort((a, b) => {
    const comparison = a.createdAt.getTime() - b.createdAt.getTime();
    return order === 'asc' ? comparison : -comparison;
  });
};

export const groupByDate = <T extends { createdAt: Date }>(items: T[]): Record<string, T[]> => {
  return items.reduce((groups, item) => {
    const dateKey = format(item.createdAt, 'yyyy-MM-dd');
    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }
    groups[dateKey].push(item);
    return groups;
  }, {} as Record<string, T[]>);
};

// Search utilities
export const searchItems = <T>(
  items: T[],
  query: string,
  searchFields: (keyof T)[]
): T[] => {
  if (!query.trim()) return items;
  
  const lowercaseQuery = query.toLowerCase();
  
  return items.filter(item =>
    searchFields.some(field => {
      const value = item[field];
      if (typeof value === 'string') {
        return value.toLowerCase().includes(lowercaseQuery);
      }
      if (Array.isArray(value)) {
        return value.some(v => 
          typeof v === 'string' && v.toLowerCase().includes(lowercaseQuery)
        );
      }
      return false;
    })
  );
};

// Validation utilities
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Local storage size utilities
export const getStorageSize = (): string => {
  let total = 0;
  for (const key in localStorage) {
    if (localStorage.hasOwnProperty(key)) {
      total += localStorage[key].length + key.length;
    }
  }
  return `${(total / 1024).toFixed(2)} KB`;
};
