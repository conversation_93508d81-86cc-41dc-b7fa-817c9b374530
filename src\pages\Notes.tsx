import React from 'react';
import { Plus, Search, Tag, BookOpen, Edit3, Calendar, Clock, Star, MoreHorizontal } from 'lucide-react';

const Notes: React.FC = () => {
  const notes = [
    {
      id: 1,
      title: 'Project Ideas',
      content: 'Brainstorming session for new features and improvements to the productivity app...',
      tags: ['work', 'ideas'],
      createdAt: '2024-01-15',
      color: 'from-blue-400 to-indigo-500',
    },
    {
      id: 2,
      title: 'Meeting Notes',
      content: 'Weekly team sync - discussed progress on Q4 goals and upcoming deadlines...',
      tags: ['meeting', 'team'],
      createdAt: '2024-01-14',
      color: 'from-emerald-400 to-teal-500',
    },
    {
      id: 3,
      title: 'Learning Resources',
      content: 'Collection of useful articles and tutorials for React and TypeScript development...',
      tags: ['learning', 'development'],
      createdAt: '2024-01-13',
      color: 'from-purple-400 to-pink-500',
    },
    {
      id: 4,
      title: 'Personal Goals',
      content: 'Setting up personal development goals for the next quarter...',
      tags: ['personal', 'goals'],
      createdAt: '2024-01-12',
      color: 'from-amber-400 to-orange-500',
    },
  ];

  return (
    <div className="space-y-8 animate-fade-in-up">
      {/* Modern Header */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-amber-500 via-orange-600 to-red-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-white/10 blur-xl animate-pulse"></div>
        <div className="absolute -bottom-4 -left-4 h-32 w-32 rounded-full bg-white/5 blur-2xl animate-pulse delay-1000"></div>

        <div className="relative z-10 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
              <BookOpen className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Notes</h1>
              <p className="text-xl text-white/90 mt-1">Capture your thoughts and ideas</p>
            </div>
          </div>
          <div className="mt-4 sm:mt-0">
            <button className="btn-modern bg-white/20 backdrop-blur-sm text-white border-white/30 hover:bg-white/30 px-6 py-3 rounded-2xl font-semibold flex items-center space-x-2 transition-all duration-300 hover:scale-105">
              <Plus className="h-5 w-5" />
              <span>New Note</span>
            </button>
          </div>
        </div>
      </div>

      {/* Modern Search and Filters */}
      <div className="rounded-3xl bg-white/80 backdrop-blur-xl p-6 shadow-xl border border-white/20">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-6">
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search notes..."
                className="block w-full pl-12 pr-4 py-3 bg-white/50 backdrop-blur-sm border border-white/20 rounded-2xl leading-5 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-amber-500/20 focus:border-amber-500/30 text-sm font-medium shadow-lg transition-all duration-200"
              />
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button className="flex items-center space-x-2 px-4 py-3 bg-gray-50 hover:bg-gray-100 rounded-2xl text-sm font-medium text-gray-700 transition-all duration-200 hover:scale-105">
              <Tag className="h-4 w-4" />
              <span>Tags</span>
            </button>
          </div>
        </div>
      </div>

      {/* Modern Notes Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {notes.map((note, index) => (
          <div
            key={note.id}
            className="group relative overflow-hidden rounded-3xl bg-white/80 backdrop-blur-xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 border border-white/20 animate-fade-in-up"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            {/* Gradient Header */}
            <div className={`h-2 bg-gradient-to-r ${note.color}`}></div>

            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">{note.title}</h3>
                <button className="p-2 rounded-xl hover:bg-gray-100 transition-colors opacity-0 group-hover:opacity-100">
                  <MoreHorizontal className="h-4 w-4 text-gray-400" />
                </button>
              </div>

              <p className="text-gray-600 text-sm mb-4 line-clamp-3">{note.content}</p>

              <div className="flex items-center justify-between">
                <div className="flex flex-wrap gap-1">
                  {note.tags.map((tag) => (
                    <span key={tag} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                      {tag}
                    </span>
                  ))}
                </div>
                <div className="flex items-center space-x-1 text-xs text-gray-500">
                  <Calendar className="h-3 w-3" />
                  <span>{note.createdAt}</span>
                </div>
              </div>
            </div>

            {/* Hover Edit Button */}
            <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
              <button className="w-10 h-10 bg-amber-500 hover:bg-amber-600 text-white rounded-full flex items-center justify-center shadow-lg transition-all duration-200 hover:scale-110">
                <Edit3 className="h-4 w-4" />
              </button>
            </div>
          </div>
        ))}

        {/* Add Note Card */}
        <div className="rounded-3xl bg-gradient-to-br from-amber-50 to-orange-50 p-8 border-2 border-dashed border-amber-200 hover:border-amber-300 transition-all duration-300 hover:scale-105 cursor-pointer group flex items-center justify-center min-h-[200px]">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto rounded-2xl bg-amber-100 flex items-center justify-center group-hover:scale-110 transition-transform mb-4">
              <Plus className="h-8 w-8 text-amber-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Add New Note</h3>
            <p className="text-gray-600">Capture your thoughts</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Notes;
