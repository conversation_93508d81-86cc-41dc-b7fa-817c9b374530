// Core data types for FocusNest application

export interface Task {
  id: string;
  title: string;
  description?: string;
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  category: string;
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Note {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Goal {
  id: string;
  title: string;
  description?: string;
  targetValue: number;
  currentValue: number;
  unit: string;
  category: string;
  deadline?: Date;
  completed: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface PomodoroSession {
  id: string;
  type: 'work' | 'shortBreak' | 'longBreak';
  duration: number; // in minutes
  startTime: Date;
  endTime?: Date;
  completed: boolean;
  taskId?: string; // optional link to a task
}

export interface PomodoroSettings {
  workDuration: number; // in minutes
  shortBreakDuration: number;
  longBreakDuration: number;
  sessionsUntilLongBreak: number;
  autoStartBreaks: boolean;
  autoStartPomodoros: boolean;
  soundEnabled: boolean;
}

export interface DailyStats {
  date: string; // YYYY-MM-DD format
  tasksCompleted: number;
  totalFocusTime: number; // in minutes
  pomodoroSessions: number;
  notesCreated: number;
  goalsProgress: number; // percentage
}

export interface AppSettings {
  theme: 'light' | 'dark' | 'system';
  notifications: boolean;
  pomodoroSettings: PomodoroSettings;
}

// Filter and sort types
export interface TaskFilters {
  category?: string;
  priority?: Task['priority'];
  completed?: boolean;
  dueDateRange?: {
    start?: Date;
    end?: Date;
  };
}

export interface NoteFilters {
  category?: string;
  tags?: string[];
  searchQuery?: string;
}

export interface GoalFilters {
  category?: string;
  completed?: boolean;
  deadline?: {
    start?: Date;
    end?: Date;
  };
}

export type SortOrder = 'asc' | 'desc';

export interface TaskSort {
  field: 'title' | 'createdAt' | 'dueDate' | 'priority';
  order: SortOrder;
}

export interface NoteSort {
  field: 'title' | 'createdAt' | 'updatedAt';
  order: SortOrder;
}

export interface GoalSort {
  field: 'title' | 'createdAt' | 'deadline' | 'progress';
  order: SortOrder;
}

// Navigation types
export type NavigationItem = {
  id: string;
  label: string;
  path: string;
  icon: string;
  badge?: number;
};

// Form types
export interface TaskFormData {
  title: string;
  description?: string;
  priority: Task['priority'];
  category: string;
  dueDate?: string; // ISO string for form handling
}

export interface NoteFormData {
  title: string;
  content: string;
  category: string;
  tags: string[];
}

export interface GoalFormData {
  title: string;
  description?: string;
  targetValue: number;
  unit: string;
  category: string;
  deadline?: string; // ISO string for form handling
}
