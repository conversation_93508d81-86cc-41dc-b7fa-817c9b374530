import React from 'react';
import { NavLink } from 'react-router-dom';
import { LayoutDashboard, CheckSquare, FileText, Target, Timer, Settings, X, Zap } from 'lucide-react';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const navigationItems = [
    {
      name: 'Dashboard',
      href: '/',
      icon: LayoutDashboard,
      gradient: 'from-blue-500 to-indigo-600',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-700',
      iconColor: 'text-blue-600',
    },
    {
      name: 'Tasks',
      href: '/tasks',
      icon: CheckSquare,
      gradient: 'from-emerald-500 to-green-600',
      bgColor: 'bg-emerald-50',
      textColor: 'text-emerald-700',
      iconColor: 'text-emerald-600',
    },
    {
      name: 'Notes',
      href: '/notes',
      icon: FileText,
      gradient: 'from-amber-500 to-yellow-600',
      bgColor: 'bg-amber-50',
      textColor: 'text-amber-700',
      iconColor: 'text-amber-600',
    },
    {
      name: 'Goals',
      href: '/goals',
      icon: Target,
      gradient: 'from-purple-500 to-violet-600',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-700',
      iconColor: 'text-purple-600',
    },
    {
      name: 'Timer',
      href: '/timer',
      icon: Timer,
      gradient: 'from-red-500 to-pink-600',
      bgColor: 'bg-red-50',
      textColor: 'text-red-700',
      iconColor: 'text-red-600',
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Settings,
      gradient: 'from-gray-500 to-slate-600',
      bgColor: 'bg-gray-50',
      textColor: 'text-gray-700',
      iconColor: 'text-gray-600',
    },
  ];

  return (
    <>
      {/* Modern Desktop Sidebar */}
      <div className="hidden lg:flex lg:flex-col lg:w-72 lg:fixed lg:inset-y-0 lg:bg-white/80 lg:backdrop-blur-xl lg:border-r lg:border-white/20 lg:shadow-2xl">
        <div className="flex flex-col flex-grow overflow-y-auto">
          {/* Modern Header with Gradient */}
          <div className="relative overflow-hidden h-20 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600">
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="relative z-10 flex items-center h-full px-6">
              <div className="w-12 h-12 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
                <Zap className="h-7 w-7 text-white" />
              </div>
              <span className="ml-4 text-2xl font-bold text-white">FocusNest</span>
            </div>
          </div>

          {/* Modern Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigationItems.map((item) => (
              <NavLink
                key={item.name}
                to={item.href}
                className={({ isActive }) =>
                  `group relative flex items-center px-4 py-3 text-sm font-semibold rounded-2xl transition-all duration-300 ${
                    isActive ? `${item.bgColor} ${item.textColor} shadow-lg transform scale-105` : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 hover:shadow-md hover:transform hover:scale-102'
                  }`
                }
              >
                {({ isActive }) => (
                  <>
                    {isActive && <div className={`absolute inset-0 bg-gradient-to-r ${item.gradient} opacity-10 rounded-2xl`}></div>}
                    <div className={`relative z-10 p-2 rounded-xl ${isActive ? 'bg-white shadow-md' : 'group-hover:bg-white/50'} transition-all duration-300`}>
                      <item.icon className={`h-5 w-5 ${isActive ? item.iconColor : 'text-gray-500 group-hover:text-gray-700'} transition-colors duration-300`} aria-hidden="true" />
                    </div>
                    <span className="relative z-10 ml-4 transition-colors duration-300">{item.name}</span>
                    {isActive && <div className={`absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-gradient-to-b ${item.gradient} rounded-l-full`}></div>}
                  </>
                )}
              </NavLink>
            ))}
          </nav>
        </div>
      </div>

      {/* Modern Mobile Sidebar */}
      <div className={`lg:hidden fixed inset-y-0 left-0 z-50 w-72 bg-white/90 backdrop-blur-xl border-r border-white/20 shadow-2xl transform transition-all duration-500 ease-out ${isOpen ? 'translate-x-0' : '-translate-x-full'}`}>
        <div className="flex flex-col flex-grow overflow-y-auto">
          {/* Mobile Header with Gradient */}
          <div className="relative overflow-hidden h-20 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600">
            <div className="absolute inset-0 bg-black/10"></div>
            <div className="relative z-10 flex items-center justify-between h-full px-6">
              <div className="flex items-center">
                <div className="w-12 h-12 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
                  <Zap className="h-7 w-7 text-white" />
                </div>
                <span className="ml-4 text-2xl font-bold text-white">FocusNest</span>
              </div>
              <button onClick={onClose} className="p-2 rounded-xl text-white/80 hover:text-white hover:bg-white/20 transition-all duration-200">
                <X className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigationItems.map((item) => (
              <NavLink
                key={item.name}
                to={item.href}
                onClick={onClose}
                className={({ isActive }) =>
                  `group relative flex items-center px-4 py-3 text-sm font-semibold rounded-2xl transition-all duration-300 ${
                    isActive ? `${item.bgColor} ${item.textColor} shadow-lg transform scale-105` : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 hover:shadow-md hover:transform hover:scale-102'
                  }`
                }
              >
                {({ isActive }) => (
                  <>
                    {isActive && <div className={`absolute inset-0 bg-gradient-to-r ${item.gradient} opacity-10 rounded-2xl`}></div>}
                    <div className={`relative z-10 p-2 rounded-xl ${isActive ? 'bg-white shadow-md' : 'group-hover:bg-white/50'} transition-all duration-300`}>
                      <item.icon className={`h-5 w-5 ${isActive ? item.iconColor : 'text-gray-500 group-hover:text-gray-700'} transition-colors duration-300`} aria-hidden="true" />
                    </div>
                    <span className="relative z-10 ml-4 transition-colors duration-300">{item.name}</span>
                    {isActive && <div className={`absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-gradient-to-b ${item.gradient} rounded-l-full`}></div>}
                  </>
                )}
              </NavLink>
            ))}
          </nav>
        </div>
      </div>

      {/* Mobile Overlay */}
      {isOpen && <div className="lg:hidden fixed inset-0 bg-black/30 backdrop-blur-sm z-40" onClick={onClose} />}
    </>
  );
};

export default Sidebar;
