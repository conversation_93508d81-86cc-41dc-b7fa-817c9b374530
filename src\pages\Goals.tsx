import React from 'react';
import { Plus, Target, TrendingUp, Calendar, Award, Star, CheckCircle2, Clock, BarChart3 } from 'lucide-react';

const Goals: React.FC = () => {
  const goals = [
    {
      id: 1,
      title: 'Complete React Certification',
      description: 'Finish the advanced React course and get certified',
      progress: 75,
      category: 'Learning',
      deadline: '2024-02-15',
      priority: 'high',
      color: 'from-blue-500 to-indigo-600'
    },
    {
      id: 2,
      title: 'Launch Personal Project',
      description: 'Build and deploy the productivity app',
      progress: 60,
      category: 'Career',
      deadline: '2024-03-01',
      priority: 'high',
      color: 'from-emerald-500 to-teal-600'
    },
    {
      id: 3,
      title: 'Read 12 Books This Year',
      description: 'Read one book per month to expand knowledge',
      progress: 25,
      category: 'Personal',
      deadline: '2024-12-31',
      priority: 'medium',
      color: 'from-purple-500 to-pink-600'
    },
    {
      id: 4,
      title: 'Exercise 3x Per Week',
      description: 'Maintain a consistent workout routine',
      progress: 80,
      category: 'Health',
      deadline: '2024-12-31',
      priority: 'medium',
      color: 'from-orange-500 to-red-600'
    }
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="space-y-8 animate-fade-in-up">
      {/* Modern Header */}
      <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-purple-600 via-indigo-600 to-blue-600 p-8 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-white/10 blur-xl animate-pulse"></div>
        <div className="absolute -bottom-4 -left-4 h-32 w-32 rounded-full bg-white/5 blur-2xl animate-pulse delay-1000"></div>

        <div className="relative z-10 flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
              <Target className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Goals</h1>
              <p className="text-xl text-white/90 mt-1">Set and track your achievements</p>
            </div>
          </div>
          <div className="mt-4 sm:mt-0">
            <button className="btn-modern bg-white/20 backdrop-blur-sm text-white border-white/30 hover:bg-white/30 px-6 py-3 rounded-2xl font-semibold flex items-center space-x-2 transition-all duration-300 hover:scale-105">
              <Plus className="h-5 w-5" />
              <span>New Goal</span>
            </button>
          </div>
        </div>
      </div>

      {/* Goals Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Target className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Goals</p>
              <p className="text-2xl font-semibold text-gray-900">0</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <TrendingUp className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-semibold text-gray-900">0</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Target className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg Progress</p>
              <p className="text-2xl font-semibold text-gray-900">0%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Goals List Placeholder */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 text-center">
          <div className="mx-auto w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
            <Target className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No goals yet</h3>
          <p className="text-gray-600 mb-4">
            Set your first goal and start tracking your progress
          </p>
          <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            Create Goal
          </button>
        </div>
      </div>
    </div>
  );
};

export default Goals;
