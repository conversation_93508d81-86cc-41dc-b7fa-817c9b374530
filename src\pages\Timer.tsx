import React, { useState, useEffect } from 'react';
import { Play, Pause, RotateCcw, Settings, Coffee, Target, Zap, Clock } from 'lucide-react';

const Timer: React.FC = () => {
  const [timeLeft, setTimeLeft] = useState(25 * 60); // 25 minutes in seconds
  const [isRunning, setIsRunning] = useState(false);
  const [mode, setMode] = useState<'work' | 'break' | 'longBreak'>('work');
  const [sessions, setSessions] = useState(0);

  const modes = {
    work: { duration: 25 * 60, label: 'Focus Time', color: 'from-red-500 to-pink-600', icon: Target },
    break: { duration: 5 * 60, label: 'Short Break', color: 'from-green-500 to-emerald-600', icon: Coffee },
    longBreak: { duration: 15 * 60, label: 'Long Break', color: 'from-blue-500 to-indigo-600', icon: Zap }
  };

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isRunning && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
    } else if (timeLeft === 0) {
      setIsRunning(false);
      // Auto switch modes
      if (mode === 'work') {
        setSessions(sessions + 1);
        if ((sessions + 1) % 4 === 0) {
          setMode('longBreak');
          setTimeLeft(modes.longBreak.duration);
        } else {
          setMode('break');
          setTimeLeft(modes.break.duration);
        }
      } else {
        setMode('work');
        setTimeLeft(modes.work.duration);
      }
    }
    return () => clearInterval(interval);
  }, [isRunning, timeLeft, mode, sessions]);

  const toggleTimer = () => {
    setIsRunning(!isRunning);
  };

  const resetTimer = () => {
    setIsRunning(false);
    setTimeLeft(modes[mode].duration);
  };

  const switchMode = (newMode: 'work' | 'break' | 'longBreak') => {
    setMode(newMode);
    setTimeLeft(modes[newMode].duration);
    setIsRunning(false);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const progress = ((modes[mode].duration - timeLeft) / modes[mode].duration) * 100;
  const circumference = 2 * Math.PI * 120;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  const currentMode = modes[mode];
  const IconComponent = currentMode.icon;

  return (
    <div className="space-y-8 animate-fade-in-up">
      {/* Modern Header */}
      <div className={`relative overflow-hidden rounded-3xl bg-gradient-to-br ${currentMode.color} p-8 text-white transition-all duration-500`}>
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute -top-4 -right-4 h-24 w-24 rounded-full bg-white/10 blur-xl animate-pulse"></div>
        <div className="absolute -bottom-4 -left-4 h-32 w-32 rounded-full bg-white/5 blur-2xl animate-pulse delay-1000"></div>
        
        <div className="relative z-10 text-center">
          <div className="flex items-center justify-center space-x-4 mb-4">
            <div className="w-12 h-12 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
              <IconComponent className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Pomodoro Timer</h1>
              <p className="text-xl text-white/90 mt-1">{currentMode.label}</p>
            </div>
          </div>
          
          <div className="flex items-center justify-center space-x-4 text-white/80">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5" />
              <span className="font-medium">Session {sessions + 1}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Mode Selector */}
      <div className="flex justify-center">
        <div className="rounded-3xl bg-white/80 backdrop-blur-xl p-2 shadow-xl border border-white/20 flex space-x-2">
          {Object.entries(modes).map(([key, modeData]) => {
            const ModeIcon = modeData.icon;
            return (
              <button
                key={key}
                onClick={() => switchMode(key as 'work' | 'break' | 'longBreak')}
                className={`flex items-center space-x-2 px-4 py-3 rounded-2xl font-medium transition-all duration-200 ${
                  mode === key
                    ? `bg-gradient-to-r ${modeData.color} text-white shadow-lg`
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <ModeIcon className="h-4 w-4" />
                <span className="text-sm">{modeData.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Timer Display */}
      <div className="flex justify-center">
        <div className="rounded-3xl bg-white/80 backdrop-blur-xl p-12 shadow-xl border border-white/20">
          <div className="text-center">
            {/* Circular Progress */}
            <div className="relative inline-flex items-center justify-center mb-8">
              <svg className="w-80 h-80 transform -rotate-90" viewBox="0 0 256 256">
                <circle
                  cx="128"
                  cy="128"
                  r="120"
                  stroke="currentColor"
                  strokeWidth="8"
                  fill="none"
                  className="text-gray-200"
                />
                <circle
                  cx="128"
                  cy="128"
                  r="120"
                  stroke="url(#gradient)"
                  strokeWidth="8"
                  fill="none"
                  strokeLinecap="round"
                  strokeDasharray={circumference}
                  strokeDashoffset={strokeDashoffset}
                  className="transition-all duration-1000 ease-out"
                />
                <defs>
                  <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" className={`${currentMode.color.split(' ')[1]}`} />
                    <stop offset="100%" className={`${currentMode.color.split(' ')[3]}`} />
                  </linearGradient>
                </defs>
              </svg>
              
              {/* Timer Text */}
              <div className="absolute inset-0 flex flex-col items-center justify-center">
                <div className="text-6xl font-bold text-gray-900 mb-2">
                  {formatTime(timeLeft)}
                </div>
                <div className="text-lg text-gray-600 font-medium">
                  {currentMode.label}
                </div>
              </div>
            </div>

            {/* Control Buttons */}
            <div className="flex items-center justify-center space-x-4">
              <button
                onClick={toggleTimer}
                className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${currentMode.color} text-white shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-110 flex items-center justify-center`}
              >
                {isRunning ? <Pause className="h-8 w-8" /> : <Play className="h-8 w-8 ml-1" />}
              </button>
              
              <button
                onClick={resetTimer}
                className="w-16 h-16 rounded-2xl bg-gray-100 hover:bg-gray-200 text-gray-600 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-110 flex items-center justify-center"
              >
                <RotateCcw className="h-6 w-6" />
              </button>
              
              <button className="w-16 h-16 rounded-2xl bg-gray-100 hover:bg-gray-200 text-gray-600 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-110 flex items-center justify-center">
                <Settings className="h-6 w-6" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="rounded-3xl bg-white/80 backdrop-blur-xl p-6 shadow-xl border border-white/20 text-center">
          <div className="w-12 h-12 mx-auto rounded-2xl bg-red-100 flex items-center justify-center mb-4">
            <Target className="h-6 w-6 text-red-600" />
          </div>
          <div className="text-2xl font-bold text-gray-900">{sessions}</div>
          <div className="text-sm text-gray-600">Completed Sessions</div>
        </div>
        
        <div className="rounded-3xl bg-white/80 backdrop-blur-xl p-6 shadow-xl border border-white/20 text-center">
          <div className="w-12 h-12 mx-auto rounded-2xl bg-green-100 flex items-center justify-center mb-4">
            <Coffee className="h-6 w-6 text-green-600" />
          </div>
          <div className="text-2xl font-bold text-gray-900">{Math.floor(sessions / 4)}</div>
          <div className="text-sm text-gray-600">Long Breaks</div>
        </div>
        
        <div className="rounded-3xl bg-white/80 backdrop-blur-xl p-6 shadow-xl border border-white/20 text-center">
          <div className="w-12 h-12 mx-auto rounded-2xl bg-blue-100 flex items-center justify-center mb-4">
            <Clock className="h-6 w-6 text-blue-600" />
          </div>
          <div className="text-2xl font-bold text-gray-900">{Math.floor(sessions * 25 / 60)}h</div>
          <div className="text-sm text-gray-600">Focus Time</div>
        </div>
      </div>
    </div>
  );
};

export default Timer;
