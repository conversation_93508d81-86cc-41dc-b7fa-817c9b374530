import React from 'react';
import { Play, Pause, Rotate<PERSON>cw, <PERSON><PERSON><PERSON> } from 'lucide-react';

const Timer: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900">Pomodoro Timer</h2>
        <p className="mt-1 text-sm text-gray-600">
          Stay focused with the Pomodoro Technique
        </p>
      </div>

      {/* Timer Display */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        <div className="text-center">
          {/* Timer Circle */}
          <div className="relative inline-flex items-center justify-center w-64 h-64 mb-8">
            <svg className="w-64 h-64 transform -rotate-90" viewBox="0 0 256 256">
              <circle
                cx="128"
                cy="128"
                r="112"
                stroke="currentColor"
                strokeWidth="8"
                fill="none"
                className="text-gray-200"
              />
              <circle
                cx="128"
                cy="128"
                r="112"
                stroke="currentColor"
                strokeWidth="8"
                fill="none"
                strokeDasharray={`${2 * Math.PI * 112}`}
                strokeDashoffset={`${2 * Math.PI * 112 * 0.25}`}
                className="text-blue-600"
                strokeLinecap="round"
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-4xl font-bold text-gray-900">25:00</div>
                <div className="text-sm text-gray-600 mt-1">Focus Time</div>
              </div>
            </div>
          </div>

          {/* Timer Controls */}
          <div className="flex items-center justify-center space-x-4">
            <button className="p-3 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 transition-colors">
              <RotateCcw className="h-6 w-6" />
            </button>
            <button className="p-4 rounded-full bg-blue-600 hover:bg-blue-700 text-white transition-colors">
              <Play className="h-8 w-8" />
            </button>
            <button className="p-3 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-600 transition-colors">
              <Settings className="h-6 w-6" />
            </button>
          </div>

          {/* Session Info */}
          <div className="mt-8 text-center">
            <p className="text-sm text-gray-600">Session 1 of 4</p>
            <p className="text-xs text-gray-500 mt-1">Next: Short Break (5 min)</p>
          </div>
        </div>
      </div>

      {/* Session Types */}
      <div className="grid grid-cols-3 gap-4">
        <button className="bg-white p-4 rounded-lg shadow-sm border-2 border-blue-600 text-center">
          <div className="text-lg font-semibold text-blue-600">Focus</div>
          <div className="text-sm text-gray-600">25 min</div>
        </button>
        <button className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 text-center hover:border-gray-300">
          <div className="text-lg font-semibold text-gray-900">Short Break</div>
          <div className="text-sm text-gray-600">5 min</div>
        </button>
        <button className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 text-center hover:border-gray-300">
          <div className="text-lg font-semibold text-gray-900">Long Break</div>
          <div className="text-sm text-gray-600">15 min</div>
        </button>
      </div>

      {/* Today's Stats */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Today's Progress</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">0</div>
            <div className="text-sm text-gray-600">Pomodoros</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">0h 0m</div>
            <div className="text-sm text-gray-600">Focus Time</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">0</div>
            <div className="text-sm text-gray-600">Breaks</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">0</div>
            <div className="text-sm text-gray-600">Tasks Done</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Timer;
