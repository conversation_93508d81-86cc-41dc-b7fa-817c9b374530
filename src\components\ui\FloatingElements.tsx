import React from 'react';

const FloatingElements: React.FC = () => {
  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden z-0">
      {/* Floating Geometric Shapes */}
      <div className="absolute top-20 left-10 w-4 h-4 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full opacity-20 animate-float"></div>
      <div className="absolute top-40 right-20 w-6 h-6 bg-gradient-to-br from-pink-400 to-red-500 rounded-full opacity-20 animate-float delay-1000"></div>
      <div className="absolute bottom-32 left-20 w-5 h-5 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full opacity-20 animate-float delay-2000"></div>
      <div className="absolute bottom-20 right-40 w-3 h-3 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full opacity-20 animate-float delay-500"></div>
      
      {/* Floating Squares */}
      <div className="absolute top-60 left-1/4 w-8 h-8 border-2 border-indigo-300/30 rounded-lg rotate-45 animate-spin-slow"></div>
      <div className="absolute bottom-60 right-1/4 w-6 h-6 border-2 border-purple-300/30 rounded-lg rotate-12 animate-bounce-slow"></div>
      
      {/* Large Background Orbs */}
      <div className="absolute -top-32 -left-32 w-64 h-64 bg-gradient-to-br from-blue-200/10 to-purple-300/10 rounded-full blur-3xl animate-pulse"></div>
      <div className="absolute -bottom-32 -right-32 w-80 h-80 bg-gradient-to-br from-pink-200/10 to-indigo-300/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-emerald-200/5 to-teal-300/5 rounded-full blur-3xl animate-pulse delay-2000"></div>
    </div>
  );
};

export default FloatingElements;
